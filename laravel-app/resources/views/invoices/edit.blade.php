@extends('layouts.rebill')

@php
    $title = "Edit Invoice";
@endphp

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-receipt me-2"></i>
                <h1 class="mb-0">
                    @if($invoice->status === 'draft')
                        Editing draft #{{ $invoice->id }}
                    @else
                        Editing recurring invoice ({{ $invoice->id }})
                    @endif
                </h1>
            </div>
            <a href="{{ url()->previous() }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back
            </a>
        </div>

        <form id="invoice-form" action="{{ route('invoices.update', $invoice) }}" method="POST">
            @csrf
            @method('PUT')

            <!-- Invoice Details Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Invoice Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rebill_title" class="form-label">Title in reBill <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="rebill_title" name="rebill_title"
                                       value="{{ old('rebill_title', $invoice->recurring_settings['rebill_title'] ?? $invoice->title) }}"
                                       placeholder="e.g. Construction Services..." required>
                                <div class="form-text">A short title to describe your invoice</div>
                            </div>
                            <div class="mb-3">
                                <label for="rebill_description" class="form-label">Description in reBill</label>
                                <textarea class="form-control" id="rebill_description" name="rebill_description" rows="3"
                                          placeholder="e.g. Ongoing development services...">{{ old('rebill_description', $invoice->recurring_settings['rebill_description'] ?? '') }}</textarea>
                                <div class="form-text">Describe the products/services</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title in Bexio</label>
                                <input type="text" class="form-control" id="title" name="title"
                                       value="{{ old('title', $invoice->recurring_settings['bexio_title'] ?? '') }}"
                                       placeholder="e.g. Construction Services...">
                                <div class="form-text">A short title to describe your invoice</div>
                            </div>
                            <div class="mb-3">
                                <label for="reference" class="form-label">Reference in Bexio</label>
                                <textarea class="form-control" id="reference" name="reference" rows="3"
                                          placeholder="e.g. Ongoing development services...">{{ old('reference', $invoice->recurring_settings['bexio_reference'] ?? '') }}</textarea>
                                <div class="form-text">Describe the product/services you're providing</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Customer Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_name" class="form-label">Customer Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="contact_name" name="contact_name"
                                       value="{{ old('contact_name', $invoice->contact_info['name'] ?? '') }}" required>
                                <div class="form-text">Customer name</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_email" class="form-label">Customer Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email"
                                       value="{{ old('contact_email', $invoice->contact_info['email'] ?? '') }}" required>
                                <div class="form-text">Customer email</div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="contact_address" class="form-label">Customer Address</label>
                                <textarea class="form-control" id="contact_address" name="contact_address" rows="2"
                                          placeholder="Customer address">{{ old('contact_address', $invoice->contact_info['address'] ?? '') }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Configuration -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Billing</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="billing_period" class="form-label">Billing Period <span class="text-danger">*</span></label>
                                <select class="form-select" id="billing_period" name="billing_period" required>
                                    @php
                                        $currentInterval = old('billing_period', $invoice->recurring_settings['interval'] ?? 'monthly');
                                    @endphp
                                    <option value="monthly" {{ $currentInterval == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                    <option value="yearly" {{ $currentInterval == 'yearly' ? 'selected' : '' }}>Yearly</option>
                                </select>
                                <div class="form-text">Your customer will be billed in this cycle</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                                @php
                                    $startDate = old('start_date');
                                    if (!$startDate && $invoice->recurringTemplate) {
                                        $startDate = $invoice->recurringTemplate->next_run;
                                    } elseif (!$startDate && isset($invoice->recurring_settings['next_charge'])) {
                                        $startDate = $invoice->recurring_settings['next_charge'];
                                    }
                                    if ($startDate) {
                                        $startDate = \Carbon\Carbon::parse($startDate)->format('Y-m-d');
                                    }
                                @endphp
                                <input type="date" class="form-control" id="start_date" name="start_date"
                                       value="{{ $startDate }}" required>
                                <div class="form-text">Invoices will be sent to your customer starting from this date</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="d-flex align-items-center" style="gap: 10px; margin-top: 20px;">
                <button type="submit" class="btn btn-primary" style="padding: 0.5rem 1rem;">
                    <i class="fas fa-receipt me-2"></i>Save Changes
                </button>
                <a href="{{ url()->previous() }}" class="btn btn-outline-secondary" style="padding: 0.5rem 1rem;">
                    <i class="fas fa-trash me-2"></i>Discard Changes
                </a>
            </div>
        </form>
    </div>
</div>

@if ($errors->any())
    <div class="alert alert-danger mt-3">
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

@endsection
