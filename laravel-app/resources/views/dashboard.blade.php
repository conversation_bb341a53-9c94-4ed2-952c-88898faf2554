@extends('layouts.app')

@section('title', 'Dashboard')

@section('content')
<div class="container-fluid">
    @if (session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="mb-2">
                <h6 class="text-muted mb-1">Welcome back, {{ Auth::user()?->name ?? 'User' }}</h6>
                <h1 class="display-5 fw-bold mb-0">Dashboard</h1>
                <p class="text-muted small mb-0">Manage your recurring invoices with Bexio integration</p>
            </div>
        </div>
        <div class="col-md-6">
            <div class="row g-3">
                <div class="col-6">
                    <div class="card bg-warning bg-opacity-10 border-warning border-opacity-25 h-100">
                        <div class="card-body p-3 text-center">
                            <h6 class="text-warning small mb-1">Unpaid Invoices</h6>
                            <h4 class="fw-bold mb-0 text-warning">{{ $unpaidInvoices }}</h4>
                            <small class="text-warning">Pending Payment</small>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="card bg-info bg-opacity-10 border-info border-opacity-25 h-100">
                        <div class="card-body p-3 text-center">
                            <h6 class="text-info small mb-1">Recurring Invoices</h6>
                            <h4 class="fw-bold mb-0 text-info">{{ count($recurringInvoices) }}</h4>
                            <small class="text-info">Active Recurring</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Kim Recurring Focus Info -->
    @if(isset($dataSource) && $dataSource === 'kim_recurring')
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-repeat me-2"></i>
                    <strong>Kim Recurring Invoices:</strong>
                    Showing {{ count($recurringInvoices) }} recurring invoices created in Kim.
                    Non-recurring invoices are handled directly in Bexio.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    @elseif(isset($dataSource) && $dataSource === 'local_database')
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Using Local Data:</strong> Unable to sync with Bexio API. Showing cached Kim invoices.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    @endif

    <!-- Main Content -->
    <div class="row g-4">
        <!-- Recurring Invoices Section -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-semibold">Upcoming Recurring Invoices</h5>
                    <a href="{{ route('invoices.create-advanced') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Create New
                    </a>
                </div>
                <div class="card-body p-0">
                    @if(count($recurringInvoices) > 0)
                        @foreach($recurringInvoices->take(5) as $index => $invoice)
                            @php
                                $nextRun = $invoice->recurringTemplate?->next_run ?? now()->addMonth();
                                $interval = $invoice->recurringTemplate?->interval ?? ($invoice->recurring_settings['interval'] ?? 'monthly');
                            @endphp
                            <div class="d-flex align-items-center p-3 {{ $index > 0 ? 'border-top' : '' }}">
                                <div class="me-3">
                                    <div class="bg-dark text-white rounded d-flex align-items-center justify-content-center flex-column" style="width: 45px; height: 45px; font-size: 0.75rem;">
                                        <div class="fw-bold">{{ $nextRun->format('d') }}</div>
                                        <div style="font-size: 0.6rem;">{{ strtoupper($nextRun->format('M')) }}</div>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $invoice->title ?? $invoice->document_nr }}</h6>
                                    <small class="text-muted">{{ $invoice->contact_info['name'] ?? 'No customer' }}</small>
                                    <div class="mt-1">
                                        @php
                                            $displayStatus = $invoice->status === 'draft' ? 'draft' : 'active';
                                            $badgeClass = $displayStatus === 'active' ? 'bg-success' : 'bg-warning';
                                            $textClass = $displayStatus === 'active' ? 'text-success' : 'text-warning';
                                        @endphp
                                        <span class="badge {{ $badgeClass }} bg-opacity-10 {{ $textClass }}">
                                            {{ ucfirst($displayStatus) }}
                                        </span>
                                        <span class="badge bg-light text-dark ms-1">{{ ucfirst($interval) }}</span>
                                        <span class="badge bg-info bg-opacity-10 text-info ms-1">CHF {{ number_format($invoice->total, 2) }}</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                        @if(count($recurringInvoices) > 5)
                            <div class="text-center p-3 border-top">
                                <a href="{{ route('invoices.index') }}" class="btn btn-outline-secondary btn-sm">View All</a>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-invoice fa-2x text-muted mb-3"></i>
                            <h6 class="text-muted">You don't have any recurring invoices</h6>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Drafts Section -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 fw-semibold">Drafts</h5>
                </div>
                <div class="card-body p-0">
                    @if(count($drafts) > 0)
                        @foreach($drafts->take(5) as $index => $draft)
                            <div class="d-flex align-items-center p-3 {{ $index > 0 ? 'border-top' : '' }}">
                                <div class="me-3">
                                    <i class="fas fa-file-alt fa-lg text-muted"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $draft->title ?? $draft->document_nr ?? 'Draft Invoice' }}</h6>
                                    <small class="text-muted">#{{ $draft->id }}</small>
                                    @if(isset($draft->contact_info['name']))
                                        <small class="text-muted"> - {{ $draft->contact_info['name'] }}</small>
                                    @endif
                                    <div class="mt-1">
                                        <span class="badge bg-warning bg-opacity-10 text-warning">Draft</span>
                                        @if($draft->total > 0)
                                            <span class="badge bg-light text-dark ms-1">CHF {{ number_format($draft->total, 2) }}</span>
                                        @endif
                                        <small class="text-muted ms-2">{{ $draft->created_at->format('M j, Y') }}</small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <a href="{{ route('invoices.edit', $draft->id) }}" class="btn btn-sm btn-outline-primary me-1">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" action="{{ route('invoices.destroy', $draft->id) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this draft?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        @endforeach
                        @if(count($drafts) > 5)
                            <div class="text-center p-3 border-top">
                                <a href="{{ route('invoices.index') }}?status=draft" class="btn btn-outline-secondary btn-sm">View All</a>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-2x text-muted mb-3"></i>
                            <h6 class="text-muted">You don't have any drafts</h6>
                        </div>
                    @endif
                </div>

                <!-- Quick Actions at bottom -->
                <div class="card-footer bg-light border-top">
                    <div class="d-grid gap-2">
                        <a href="{{ route('invoices.create-advanced') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Create New Invoice
                        </a>
                        <div class="row g-2">
                            <div class="col-6">
                                <a href="{{ route('settings.index') }}" class="btn btn-outline-secondary btn-sm w-100">
                                    <i class="fas fa-cog me-1"></i>Settings
                                </a>
                            </div>
                            <div class="col-6">
                                <form method="POST" action="{{ route('logout') }}" class="mb-0">
                                    @csrf
                                    <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
