@extends('layouts.app')

@section('title', 'Drafts')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row align-items-center justify-content-between mb-4">
        <div class="col-auto">
            <div class="mb-2">
                <h6 class="text-muted mb-1 fw-bold" style="font-size: 20px;">Invoice</h6>
                <h1 class="fw-bold mb-0" style="font-size: 40px; line-height: 1;">Drafts</h1>
            </div>
        </div>
        <div class="col-auto">
            <a href="{{ route('invoices.create-advanced') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create New
            </a>
        </div>
    </div>

    <!-- Drafts List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-light" style="border-radius: 15px; overflow: hidden;">
                <div class="card-body p-0">
                    @if($drafts->count() > 0)
                        @foreach($drafts as $index => $draft)
                            @if($index > 0)
                                <hr class="m-0" style="opacity: 0.5;">
                            @endif
                            
                            <div class="draft-item d-flex align-items-center justify-content-between p-3 position-relative" 
                                 style="transition: background-color 0.2s ease; cursor: pointer;"
                                 onmouseover="this.style.backgroundColor='#f8f9fa'"
                                 onmouseout="this.style.backgroundColor='#e9ecef'"
                                 onclick="window.location.href='{{ route('invoices.edit', $draft->id) }}'">
                                
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-medium">
                                        {{ $draft->title ?? $draft->recurring_settings['rebill_description'] ?? 'Created ' . $draft->created_at->format('M j, Y') }}
                                    </h6>
                                    <small class="text-muted">#{{ $draft->id }}</small>
                                </div>
                                
                                <div class="draft-actions" style="opacity: 0; transition: opacity 0.2s ease;">
                                    <div class="d-flex align-items-center" style="gap: 10px;">
                                        <form method="POST" action="{{ route('invoices.destroy', $draft->id) }}" 
                                              class="mb-0" 
                                              onsubmit="return confirm('Are you sure you want to delete this draft?')"
                                              onclick="event.stopPropagation();">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" style="border: none; background: none;">
                                                <i class="fas fa-trash" style="color: #6c757d;"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="d-flex align-items-center justify-content-center flex-column text-center py-5" style="gap: 10px; padding: 50px 0;">
                            <i class="fas fa-file-alt fa-2x text-muted"></i>
                            <p class="fw-medium text-muted mb-0">You don't have any drafts yet</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.draft-item:hover .draft-actions {
    opacity: 1 !important;
}

.draft-item {
    background-color: #e9ecef;
}

.draft-item:hover {
    background-color: #f8f9fa !important;
}
</style>
@endsection
