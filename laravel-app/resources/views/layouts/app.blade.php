<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><PERSON> - @yield('title')</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .card {
            border-radius: 10px;
        }
        .shadow-sm {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
        }

        /* Sidebar Styles */
        .offcanvas {
            width: 280px !important;
        }

        .nav-link {
            transition: all 0.2s ease;
            border-radius: 0;
        }

        .nav-link:hover {
            background-color: rgba(13, 110, 253, 0.1) !important;
            color: #0d6efd !important;
        }

        .nav-link.active {
            background-color: rgba(13, 110, 253, 0.1) !important;
            color: #0d6efd !important;
            border-right: 3px solid #0d6efd !important;
        }

        /* Hamburger button */
        .btn-outline-secondary {
            border: 1px solid #dee2e6;
            color: #6c757d;
        }

        .btn-outline-secondary:hover {
            background-color: #6c757d;
            border-color: #6c757d;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Navigation -->
        <nav class="navbar navbar-light bg-white shadow-sm">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <!-- Hamburger Menu Button -->
                    <button class="btn btn-outline-secondary me-3" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu">
                        <i class="fas fa-bars"></i>
                    </button>

                    <a class="navbar-brand fw-bold" href="{{ route('dashboard') }}">
                        <i class="fas fa-receipt me-2 text-primary"></i>Kim Rebill
                    </a>
                </div>

                <!-- User Avatar Dropdown -->
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                            <span class="fw-bold">{{ substr(Auth::user()?->name ?? 'U', 0, 1) }}</span>
                        </div>
                        <span class="d-none d-md-inline">{{ Auth::user()?->name ?? 'User' }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item" href="{{ route('settings.index') }}">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('logout') }}" class="mb-0">
                                @csrf
                                <button type="submit" class="dropdown-item text-danger">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Sidebar Offcanvas -->
        <div class="offcanvas offcanvas-start" tabindex="-1" id="sidebarMenu" aria-labelledby="sidebarMenuLabel">
            <div class="offcanvas-header border-bottom">
                <h5 class="offcanvas-title fw-bold" id="sidebarMenuLabel">
                    <i class="fas fa-receipt me-2 text-primary"></i>Kim Rebill
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body p-0">
                <nav class="nav flex-column">
                    <a class="nav-link d-flex align-items-center py-3 px-4 {{ request()->routeIs('dashboard') ? 'bg-primary bg-opacity-10 text-primary border-end border-primary border-3' : 'text-dark' }}" href="{{ route('dashboard') }}">
                        <i class="fas fa-tachometer-alt me-3"></i>
                        <span class="fw-medium">Dashboard</span>
                    </a>
                    <a class="nav-link d-flex align-items-center py-3 px-4 {{ request()->routeIs('invoices.*') ? 'bg-primary bg-opacity-10 text-primary border-end border-primary border-3' : 'text-dark' }}" href="{{ route('invoices.index') }}">
                        <i class="fas fa-file-invoice me-3"></i>
                        <span class="fw-medium">Invoices</span>
                    </a>
                    <a class="nav-link d-flex align-items-center py-3 px-4 {{ request()->routeIs('invoices.index') && request()->get('status') === 'draft' ? 'bg-primary bg-opacity-10 text-primary border-end border-primary border-3' : 'text-dark' }}" href="{{ route('invoices.index') }}?status=draft">
                        <i class="fas fa-file-alt me-3"></i>
                        <span class="fw-medium">Drafts</span>
                    </a>
                </nav>
            </div>
        </div>

        <main class="py-4">
            @yield('content')
        </main>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
