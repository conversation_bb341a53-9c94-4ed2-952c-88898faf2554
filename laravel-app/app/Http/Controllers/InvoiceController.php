<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Invoice;
use App\Models\RecurringTemplate;
use App\Services\BexioServiceInterface;

class InvoiceController extends Controller
{
    public function __construct(
        private BexioServiceInterface $bexioService
    ) {}

    public function create()
    {
        return view('invoices.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'rebill_title' => 'required|string|max:255',
            'rebill_description' => 'nullable|string',
            'title' => 'nullable|string|max:255',
            'reference' => 'nullable|string',
            'contact_name' => 'required|string|max:255',
            'contact_email' => 'required|email|max:255',
            'contact_address' => 'nullable|string',
            'total' => 'required|numeric|min:0',
            'billing_period' => 'required|in:monthly,yearly',
            'start_date' => 'required|date',
            'action' => 'required|in:create,save_draft'
        ]);

        $user = Auth::user();

        // Determine status based on action
        $status = $request->action === 'save_draft' ? 'draft' : 'active';

        // Create invoice (always recurring)
        $invoice = Invoice::create([
            'user_id' => $user->id,
            'organization_id' => $user->organization_id,
            'bexio_id' => $status === 'active' ? 'INV-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT) : null,
            'title' => $request->rebill_title, // Use rebill_title as main title
            'document_nr' => 'INV-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
            'contact_info' => [
                'name' => $request->contact_name,
                'email' => $request->contact_email,
                'address' => $request->contact_address
            ],
            'total' => $request->total,
            'status' => $status,
            'is_recurring' => true, // Always recurring
            'recurring_settings' => [
                'interval' => $request->billing_period,
                'next_charge' => $request->start_date,
                'rebill_title' => $request->rebill_title,
                'rebill_description' => $request->rebill_description,
                'bexio_title' => $request->title,
                'bexio_reference' => $request->reference
            ]
        ]);

        // Create recurring template (always create since all invoices are recurring)
        RecurringTemplate::create([
            'organization_id' => $user->organization_id,
            'invoice_id' => $invoice->id,
            'interval' => $request->billing_period,
            'next_run' => $request->start_date
        ]);

        $message = $request->action === 'save_draft' ? 'Draft saved successfully!' : 'Invoice created successfully!';
        return redirect()->route('dashboard')->with('success', $message);
    }

    public function index()
    {
        $user = Auth::user();

        // Get all invoices for the user with recurring templates
        $invoices = Invoice::where('user_id', $user->id)
            ->with('recurringTemplate')
            ->orderBy('created_at', 'desc')
            ->get(); // Use get() instead of paginate() for metrics calculation

        return view('invoices.index', compact('invoices'));
    }

    public function drafts()
    {
        $user = Auth::user();

        // Get only draft invoices for the user
        $drafts = Invoice::where('user_id', $user->id)
            ->where('status', 'draft')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('drafts.index', compact('drafts'));
    }

    public function show(Invoice $invoice)
    {
        // Ensure user can only view their own invoices
        if ($invoice->user_id !== Auth::id()) {
            abort(403);
        }

        return view('invoices.show', compact('invoice'));
    }

    public function edit(Invoice $invoice)
    {
        // Ensure user can only edit their own invoices
        if ($invoice->user_id !== Auth::id()) {
            abort(403);
        }

        return view('invoices.edit', compact('invoice'));
    }

    public function update(Request $request, Invoice $invoice)
    {
        // Ensure user can only update their own invoices
        if ($invoice->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'rebill_title' => 'required|string|max:255',
            'rebill_description' => 'nullable|string',
            'title' => 'nullable|string|max:255',
            'reference' => 'nullable|string',
            'contact_name' => 'required|string|max:255',
            'contact_email' => 'required|email|max:255',
            'contact_address' => 'nullable|string',
            'total' => 'required|numeric|min:0',
            'is_recurring' => 'boolean',
            'recurring_interval' => 'nullable|in:daily,weekly,monthly',
            'next_run_date' => 'nullable|date'
        ]);

        $user = Auth::user();

        // Update invoice
        $invoice->update([
            'title' => $request->rebill_title, // Use rebill_title as main title
            'contact_info' => [
                'name' => $request->contact_name,
                'email' => $request->contact_email,
                'address' => $request->contact_address
            ],
            'total' => $request->total,
            'is_recurring' => $request->boolean('is_recurring'),
            'recurring_settings' => [
                'interval' => $request->recurring_interval,
                'next_charge' => $request->next_run_date,
                'rebill_title' => $request->rebill_title,
                'rebill_description' => $request->rebill_description,
                'bexio_title' => $request->title,
                'bexio_reference' => $request->reference
            ]
        ]);

        // Update or create recurring template if needed
        if ($request->boolean('is_recurring') && $request->recurring_interval && $request->next_run_date) {
            $invoice->recurringTemplate()->updateOrCreate(
                ['invoice_id' => $invoice->id],
                [
                    'organization_id' => $user->organization_id,
                    'interval' => $request->recurring_interval,
                    'next_run' => $request->next_run_date
                ]
            );
        } else {
            // Remove recurring template if not recurring anymore
            $invoice->recurringTemplate()->delete();
        }

        return redirect()->route('invoices.index')->with('success', 'Invoice updated successfully!');
    }

    public function destroy(Invoice $invoice)
    {
        // Ensure user can only delete their own invoices
        if ($invoice->user_id !== Auth::id()) {
            abort(403);
        }

        $invoice->delete();

        return redirect()->route('invoices.index')->with('success', 'Invoice deleted successfully!');
    }
}
